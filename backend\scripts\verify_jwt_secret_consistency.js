#!/usr/bin/env node

/**
 * JWT_SECRET一致性验证工具
 * 验证当前JWT_SECRET是否与KV数据中的密码哈希一致
 */

require('dotenv').config();
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 密码加密函数
async function bcrypt(password, jwtSecret) {
  const data = password + jwtSecret;
  const hash = crypto.createHash('sha256').update(data).digest();
  return Buffer.from(hash).toString('base64');
}

// 常见密码列表
const COMMON_PASSWORDS = [
  '123456', 'password', '123456789', '12345678', '12345',
  '1234567', '1234567890', 'qwerty', 'abc123', 'password123',
  'admin', 'root', 'user', 'test', '555', '888888', '666666',
  '111111', '000000', '999999', '123123', 'admin123'
];

class JWTSecretVerifier {
  constructor() {
    this.currentJwtSecret = process.env.JWT_SECRET;
    this.dataDir = path.join(__dirname, '../data');
    
    if (!this.currentJwtSecret) {
      log('red', '❌ 错误: 未找到JWT_SECRET环境变量');
      process.exit(1);
    }
  }

  // 查找最新的用户备份文件
  async findLatestUserBackup() {
    try {
      const files = await fs.readdir(this.dataDir);
      const userBackups = files
        .filter(file => file.startsWith('kv_backup_users_') && file.endsWith('.json'))
        .sort()
        .reverse();
      
      if (userBackups.length === 0) {
        throw new Error('未找到用户数据备份文件');
      }
      
      return path.join(this.dataDir, userBackups[0]);
    } catch (error) {
      throw new Error(`查找备份文件失败: ${error.message}`);
    }
  }

  // 加载备份数据
  async loadBackupData(filepath) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      const backupData = JSON.parse(content);
      
      if (!backupData.data) {
        throw new Error('备份文件格式错误：缺少data字段');
      }
      
      return backupData;
    } catch (error) {
      throw new Error(`加载备份文件失败: ${error.message}`);
    }
  }

  // 尝试破解单个用户的密码
  async crackUserPassword(username, storedHash, passwords) {
    for (const password of passwords) {
      const computedHash = await bcrypt(password, this.currentJwtSecret);
      if (computedHash === storedHash) {
        return { password, success: true };
      }
    }
    return { password: null, success: false };
  }

  // 验证JWT_SECRET一致性
  async verifyConsistency() {
    try {
      log('blue', '🔐 JWT_SECRET一致性验证工具');
      log('blue', '=' .repeat(60));
      
      // 加载备份数据
      const backupFile = await this.findLatestUserBackup();
      log('cyan', `📁 使用备份文件: ${path.basename(backupFile)}`);
      
      const backupData = await this.loadBackupData(backupFile);
      const userData = backupData.data;
      
      // 获取用户数据
      const userKeys = Object.keys(userData).filter(key => key.startsWith('user:'));
      log('cyan', `👥 找到 ${userKeys.length} 个用户`);
      
      if (userKeys.length === 0) {
        log('yellow', '⚠️  没有用户数据可验证');
        return;
      }
      
      log('yellow', `🔑 当前JWT_SECRET: ${this.currentJwtSecret.substring(0, 10)}...`);
      log('blue', '=' .repeat(60));
      
      let successCount = 0;
      let totalTested = 0;
      const maxTestUsers = 10; // 限制测试用户数量
      
      log('cyan', `🧪 开始验证（测试前${maxTestUsers}个用户）...`);
      
      for (let i = 0; i < Math.min(userKeys.length, maxTestUsers); i++) {
        const key = userKeys[i];
        const username = key.replace('user:', '');
        
        try {
          const userInfo = JSON.parse(userData[key]);
          const storedHash = userInfo.passwordHash;
          
          if (!storedHash) {
            log('yellow', `⚠️  用户 ${username}: 无密码哈希`);
            continue;
          }
          
          totalTested++;
          process.stdout.write(`\r[${i + 1}/${maxTestUsers}] 测试用户: ${username.padEnd(15)}`);
          
          // 尝试破解密码
          const result = await this.crackUserPassword(username, storedHash, COMMON_PASSWORDS);
          
          if (result.success) {
            successCount++;
            process.stdout.write(` ✅ 密码: "${result.password}"\n`);
          } else {
            process.stdout.write(` ❌ 未破解\n`);
          }
          
        } catch (error) {
          process.stdout.write(` ❌ 错误: ${error.message}\n`);
        }
      }
      
      console.log(); // 换行
      log('blue', '=' .repeat(60));
      
      // 显示结果
      const successRate = totalTested > 0 ? (successCount / totalTested * 100).toFixed(1) : 0;
      
      log('green', `📊 验证结果:`);
      log('cyan', `  测试用户数: ${totalTested}`);
      log('cyan', `  成功破解: ${successCount}`);
      log('cyan', `  成功率: ${successRate}%`);
      
      // 判断一致性
      if (successRate >= 80) {
        log('green', '\n✅ JWT_SECRET一致性验证通过！');
        log('green', '   当前JWT_SECRET与KV数据中的密码哈希一致');
        log('green', '   迁移脚本可以安全使用');
      } else if (successRate >= 20) {
        log('yellow', '\n⚠️  JWT_SECRET一致性部分通过');
        log('yellow', '   可能存在多个不同的JWT_SECRET');
        log('yellow', '   建议进一步调查');
      } else {
        log('red', '\n❌ JWT_SECRET一致性验证失败！');
        log('red', '   当前JWT_SECRET与KV数据不一致');
        log('red', '   迁移后用户将无法登录');
        log('red', '   请检查JWT_SECRET配置');
      }
      
      // 提供建议
      log('blue', '\n💡 建议:');
      if (successRate < 80) {
        log('cyan', '1. 检查老系统的JWT_SECRET配置');
        log('cyan', '2. 确保新系统使用相同的JWT_SECRET');
        log('cyan', '3. 如果JWT_SECRET已变更，考虑密码重置机制');
      } else {
        log('cyan', '1. 可以安全进行用户迁移');
        log('cyan', '2. 迁移后用户可以正常登录');
        log('cyan', '3. 建议迁移后进行登录测试');
      }
      
    } catch (error) {
      log('red', `❌ 验证失败: ${error.message}`);
      throw error;
    }
  }
}

// 执行验证
if (require.main === module) {
  const verifier = new JWTSecretVerifier();
  verifier.verifyConsistency().catch(error => {
    log('red', `验证失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = JWTSecretVerifier;
