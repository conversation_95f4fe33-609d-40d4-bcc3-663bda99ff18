# 用户数据迁移脚本使用说明

## 📋 概述

`migrate_users_only.js` 是专门用于迁移用户数据的脚本，从本地KV备份文件读取用户数据并安全地迁移到PostgreSQL数据库。

## 🎯 功能特点

- ✅ **专注用户数据**：只处理用户数据，不涉及卡密、语音映射等
- ✅ **数据验证**：完整的数据验证和转换逻辑
- ✅ **类型映射**：自动处理T→PT的VIP类型转换
- ✅ **安全迁移**：重复用户跳过，事务保护
- ✅ **详细日志**：实时进度显示和错误统计
- ✅ **兼容性处理**：自动处理新旧用户数据结构

## 🚀 使用方法

### 基本用法

```bash
# 进入后端目录
cd backend

# 使用最新的备份文件（自动查找）
node scripts/migrate_users_only.js

# 指定特定的备份文件
node scripts/migrate_users_only.js data/kv_backup_users_20250726_114649.json

# 使用绝对路径
node scripts/migrate_users_only.js D:\myaitts\backend\data\kv_backup_users.json
```

### 环境要求

确保 `.env` 文件中配置了数据库连接：

```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

## 📊 执行过程示例

```
🚀 开始用户数据迁移...

📁 使用备份文件: kv_backup_users_20250726_114649.json
✅ 备份数据加载成功
📊 备份信息: 539 个键，下载时间: 2025-07-26T11:55:15.600Z
✅ 数据库连接成功

🚀 开始迁移用户数据...

📊 找到 343 个用户待迁移

[1/343] 处理用户: user001              ✅ PM (新用户)
[2/343] 处理用户: user002              ✅ M (老用户)
[3/343] 处理用户: user003              ⚠️  用户 user003 已存在，跳过
[4/343] 处理用户: user004              ✅ 无VIP (新用户)
[5/343] 处理用户: user005              ❌ 失败: 数据转换失败
...
[343/343] 处理用户: user343            ✅ PT (新用户)

📊 迁移统计结果
==================================================
✅ 成功迁移: 320 个用户
⚠️  跳过重复: 18 个用户
❌ 迁移失败: 5 个用户
📊 总计处理: 343 个用户
📈 成功率: 93.3%

❌ 错误详情:
  • user005: 缺少必需字段: passwordHash
  • user123: 数据转换失败: Invalid JSON
  • user234: 数据库约束违反

🎉 用户数据迁移完成！

💡 下一步建议:
1. 验证用户登录功能
2. 检查VIP状态和配额计算
3. 测试使用统计功能
```

## 🔧 数据转换逻辑

### VIP类型映射

```javascript
const VIP_TYPE_MAPPING = {
  'T': 'PT',    // 旧测试套餐 → 新测试套餐
  'PT': 'PT',   // 新测试套餐
  'M': 'M',     // 月套餐
  'Q': 'Q',     // 季套餐
  'H': 'H',     // 半年套餐
  'PM': 'PM',   // 永久月套餐
  'PQ': 'PQ',   // 永久季套餐
  'PH': 'PH'    // 永久半年套餐
};
```

### 数据结构转换

#### 输入格式（KV数据）
```json
{
  "username": "eluzh",
  "passwordHash": "cJCbjhbPlLry/62n/mQkGtjUiVrigTb77M0OWtvtQJU=",
  "email": "<EMAIL>",
  "vip": {
    "expireAt": 1756020412987,
    "type": "M",
    "quotaChars": 82766,
    "usedChars": 4481
  },
  "usage": {
    "totalChars": 6715,
    "monthlyChars": 6715,
    "monthlyResetAt": 1754006400000
  }
}
```

#### 输出格式（数据库）
```sql
INSERT INTO users (
  username,           -- 'eluzh'
  password_hash,      -- 'cJCbjhbPlLry/62n/mQkGtjUiVrigTb77M0OWtvtQJU='
  email,              -- '<EMAIL>'
  vip_info,           -- JSON: {"expireAt":1756020412987,"type":"M","quotaChars":82766,"usedChars":4481}
  usage_stats,        -- JSON: {"totalChars":6715,"monthlyChars":6715,"monthlyResetAt":1754006400000}
  created_at,         -- CURRENT_TIMESTAMP
  updated_at          -- CURRENT_TIMESTAMP
)
```

### 默认值处理

#### 无VIP信息的用户
```json
{
  "vip_info": {
    "expireAt": 0,
    "type": null,
    "quotaChars": undefined,
    "usedChars": undefined
  }
}
```

#### 无使用统计的用户
```json
{
  "usage_stats": {
    "totalChars": 0,
    "monthlyChars": 0,
    "monthlyResetAt": 1756108800000  // 下个月1号
  }
}
```

## 🛡️ 安全特性

### 1. 事务保护
- 每个用户插入都在独立事务中
- 失败自动回滚，不影响其他用户

### 2. 重复检查
- 自动检查用户名是否已存在
- 重复用户跳过，不会覆盖现有数据

### 3. 数据验证
- 验证必需字段（username, passwordHash）
- JSON格式验证
- 数据类型检查

### 4. 错误处理
- 详细的错误日志
- 继续处理其他用户，不因单个错误中断

## 📋 迁移前检查清单

- [ ] 备份现有数据库
- [ ] 确认数据库连接配置正确
- [ ] 确认备份文件路径正确
- [ ] 检查磁盘空间充足
- [ ] 确认数据库表结构已创建

## 🔍 迁移后验证

### 1. 数据完整性检查
```sql
-- 检查用户总数
SELECT COUNT(*) FROM users;

-- 检查VIP类型分布
SELECT 
  vip_info->>'type' as vip_type, 
  COUNT(*) as count 
FROM users 
GROUP BY vip_info->>'type';

-- 检查老用户vs新用户
SELECT 
  CASE 
    WHEN vip_info->>'quotaChars' IS NULL THEN '老用户'
    ELSE '新用户'
  END as user_type,
  COUNT(*) as count
FROM users 
GROUP BY (vip_info->>'quotaChars' IS NULL);
```

### 2. 功能验证
- 测试用户登录
- 检查VIP状态显示
- 验证配额计算
- 测试使用统计更新

## ⚠️ 注意事项

1. **备份重要性**：迁移前务必备份现有数据库
2. **测试环境**：建议先在测试环境验证
3. **分批迁移**：大量用户可考虑分批迁移
4. **监控资源**：注意数据库连接数和内存使用
5. **错误处理**：关注错误日志，及时处理失败用户

## 🚨 故障排除

### 常见错误

1. **数据库连接失败**
   ```
   ❌ 迁移失败: connection refused
   ```
   **解决**：检查DATABASE_URL配置和数据库服务状态

2. **备份文件不存在**
   ```
   ❌ 查找备份文件失败: 未找到用户数据备份文件
   ```
   **解决**：确认备份文件路径，或先运行下载脚本

3. **数据格式错误**
   ```
   ❌ 数据转换失败: Invalid JSON
   ```
   **解决**：检查备份文件完整性，重新下载

4. **权限不足**
   ```
   ❌ permission denied for table users
   ```
   **解决**：确认数据库用户有INSERT权限

---

**重要提醒**：迁移是不可逆操作，请务必在生产环境执行前充分测试！
